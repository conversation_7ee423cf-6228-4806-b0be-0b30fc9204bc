# تطبيق قائمة المهام البسيطة

تطبيق أندرويد بسيط لإدارة المهام اليومية، مصمم لأغراض التدريب والتعلم.

## المميزات

- ✅ إضافة مهام جديدة
- ✅ وضع علامة على المهام المكتملة
- ✅ حذف المهام
- ✅ واجهة مستخدم بسيطة وسهلة الاستخدام
- ✅ دعم اللغة العربية

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **Android SDK** - منصة التطوير
- **RecyclerView** - لعرض قائمة المهام
- **ViewBinding** - لربط العناصر
- **Material Design** - للتصميم

## هيكل المشروع

```
app/
├── src/main/
│   ├── java/com/example/simpletodo/
│   │   ├── MainActivity.kt          # النشاط الرئيسي
│   │   ├── Task.kt                  # نموذج البيانات للمهمة
│   │   └── TaskAdapter.kt           # محول قائمة المهام
│   ├── res/
│   │   ├── layout/
│   │   │   ├── activity_main.xml    # تخطيط الشاشة الرئيسية
│   │   │   └── item_task.xml        # تخطيط عنصر المهمة
│   │   ├── values/
│   │   │   ├── strings.xml          # النصوص
│   │   │   ├── colors.xml           # الألوان
│   │   │   └── themes.xml           # الثيمات
│   │   └── drawable/
│   │       └── edit_text_background.xml
│   └── AndroidManifest.xml
└── build.gradle
```

## كيفية الاستخدام

1. **إضافة مهمة جديدة:**
   - اكتب نص المهمة في الحقل النصي
   - اضغط على زر "إضافة مهمة"

2. **إكمال مهمة:**
   - اضغط على مربع الاختيار بجانب المهمة
   - ستظهر المهمة بخط مشطوب

3. **حذف مهمة:**
   - اضغط على أيقونة الحذف بجانب المهمة

## متطلبات التشغيل

- Android API Level 24 (Android 7.0) أو أحدث
- Android Studio Arctic Fox أو أحدث
- Kotlin 1.9.10 أو أحدث

## كيفية تشغيل المشروع

1. افتح Android Studio
2. اختر "Open an existing project"
3. حدد مجلد المشروع
4. انتظر حتى يتم تحميل المشروع
5. اضغط على "Run" أو Shift+F10

## الملاحظات التعليمية

هذا التطبيق مصمم لأغراض التدريب ويغطي المفاهيم التالية:

- **Activity Lifecycle** - دورة حياة النشاط
- **RecyclerView & Adapter Pattern** - قوائم ديناميكية
- **Data Classes** - فئات البيانات في Kotlin
- **View Binding** - ربط العناصر
- **Event Handling** - التعامل مع الأحداث
- **Material Design** - تصميم المواد

## التطوير المستقبلي

يمكن تطوير التطبيق بإضافة:
- حفظ البيانات في قاعدة بيانات محلية
- إضافة تواريخ استحقاق للمهام
- تصنيف المهام حسب الأولوية
- مشاركة المهام
- إشعارات التذكير
