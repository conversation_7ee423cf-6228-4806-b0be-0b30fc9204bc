package com.example.simpletodo

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.simpletodo.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var taskAdapter: TaskAdapter
    private val tasks = mutableListOf<Task>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupRecyclerView()
        setupClickListeners()
        updateEmptyState()
    }

    private fun setupRecyclerView() {
        taskAdapter = TaskAdapter(
            tasks = tasks,
            onTaskChecked = { task, isChecked ->
                task.isCompleted = isChecked
                taskAdapter.updateTask(task)
                showTaskStatusMessage(task)
            },
            onTaskDeleted = { task ->
                taskAdapter.removeTask(task)
                updateEmptyState()
                Toast.makeText(this, "تم حذف المهمة", Toast.LENGTH_SHORT).show()
            }
        )
        
        binding.recyclerViewTasks.apply {
            adapter = taskAdapter
            layoutManager = LinearLayoutManager(this@MainActivity)
        }
    }

    private fun setupClickListeners() {
        binding.buttonAdd.setOnClickListener {
            addNewTask()
        }
        
        // Allow adding task by pressing Enter
        binding.editTextTask.setOnEditorActionListener { _, _, _ ->
            addNewTask()
            true
        }
    }

    private fun addNewTask() {
        val taskText = binding.editTextTask.text.toString().trim()
        
        if (taskText.isNotEmpty()) {
            val newTask = Task(text = taskText)
            taskAdapter.addTask(newTask)
            binding.editTextTask.text.clear()
            updateEmptyState()
            Toast.makeText(this, "تم إضافة المهمة", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "يرجى كتابة نص المهمة", Toast.LENGTH_SHORT).show()
        }
    }

    private fun updateEmptyState() {
        if (tasks.isEmpty()) {
            binding.recyclerViewTasks.visibility = View.GONE
            binding.textViewEmpty.visibility = View.VISIBLE
        } else {
            binding.recyclerViewTasks.visibility = View.VISIBLE
            binding.textViewEmpty.visibility = View.GONE
        }
    }

    private fun showTaskStatusMessage(task: Task) {
        val message = if (task.isCompleted) {
            getString(R.string.task_completed)
        } else {
            getString(R.string.task_pending)
        }
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
