package com.example.simpletodo

import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.simpletodo.databinding.ItemTaskBinding

class TaskAdapter(
    private val tasks: MutableList<Task>,
    private val onTaskChecked: (Task, Boolean) -> Unit,
    private val onTaskDeleted: (Task) -> Unit
) : RecyclerView.Adapter<TaskAdapter.TaskViewHolder>() {

    class TaskViewHolder(private val binding: ItemTaskBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            task: Task,
            onTaskChecked: (Task, Boolean) -> Unit,
            onTaskDeleted: (Task) -> Unit
        ) {
            binding.textViewTask.text = task.text
            binding.checkBoxTask.isChecked = task.isCompleted
            
            // Apply strikethrough effect if task is completed
            if (task.isCompleted) {
                binding.textViewTask.paintFlags = binding.textViewTask.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                binding.textViewTask.alpha = 0.6f
            } else {
                binding.textViewTask.paintFlags = binding.textViewTask.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                binding.textViewTask.alpha = 1.0f
            }
            
            binding.checkBoxTask.setOnCheckedChangeListener { _, isChecked ->
                onTaskChecked(task, isChecked)
            }
            
            binding.buttonDelete.setOnClickListener {
                onTaskDeleted(task)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TaskViewHolder {
        val binding = ItemTaskBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TaskViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TaskViewHolder, position: Int) {
        holder.bind(tasks[position], onTaskChecked, onTaskDeleted)
    }

    override fun getItemCount(): Int = tasks.size

    fun addTask(task: Task) {
        tasks.add(0, task) // Add to the beginning of the list
        notifyItemInserted(0)
    }

    fun removeTask(task: Task) {
        val position = tasks.indexOf(task)
        if (position != -1) {
            tasks.removeAt(position)
            notifyItemRemoved(position)
        }
    }

    fun updateTask(task: Task) {
        val position = tasks.indexOf(task)
        if (position != -1) {
            notifyItemChanged(position)
        }
    }
}
